using System;

namespace BusinessObjects
{
    public class Product
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public int? CategoryId { get; set; }
        public short? UnitsInStock { get; set; }
        public decimal? UnitPrice { get; set; }

        // Navigation property (optional for in-memory, but good practice for EF)
        public virtual Category Category { get; set; }
    }
}


