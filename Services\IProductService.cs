using BusinessObjects;
using System.Collections.Generic;
using System.Linq;

namespace Services
{
    public interface IProductService
    {
        List<Product> GetProducts();
        Product GetProductById(int productId);
        void AddProduct(Product product);
        void UpdateProduct(Product product);
        void DeleteProduct(int productId);
        
        // LINQ operations
        List<Product> GetProductsByCategory(int categoryId);
        List<Product> GetProductsByPriceRange(decimal minPrice, decimal maxPrice);
        List<Product> SearchProductsByName(string searchTerm);
        List<Product> GetProductsSortedByPrice(bool ascending = true);
        List<Product> GetProductsSortedByStock(bool ascending = true);
        List<Product> GetTopExpensiveProducts(int count);
        List<Product> GetLowStockProducts(int threshold);
        decimal GetAveragePrice();
        decimal GetTotalInventoryValue();
        int GetProductCountByCategory(int categoryId);
        List<IGrouping<int?, Product>> GetProductsGroupedByCategory();
    }
}

