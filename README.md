# Product Management Demo - README

## Overview

This is a complete Product Management application built using WPF (Windows Presentation Foundation) and C#, demonstrating the use of LINQ operations and layered architecture.

## Architecture

The application follows a layered architecture pattern with the following projects:

### 1. BusinessObjects

Contains Plain Old C# Objects (POCOs) that define the data structure:

- `Product.cs` - Defines product properties (ProductId, ProductName, CategoryId, UnitsInStock, UnitPrice)
- `Category.cs` - Defines category properties (CategoryId, CategoryName)
- `AccountMember.cs` - Defines user account properties for authentication

### 2. DataAccessLayer (DAL)

Manages in-memory data storage using `List<T>` collections:

- `ProductDAO.cs` - Manages product data with CRUD operations
- `CategoryDAO.cs` - Provides predefined category data
- `AccountDAO.cs` - Provides authentication data

### 3. Repositories

Implements the Repository pattern to abstract data access:

- `IProductRepository.cs` / `ProductRepository.cs` - Product data operations
- `ICategoryRepository.cs` / `CategoryRepository.cs` - Category data operations
- `IAccountRepository.cs` / `AccountRepository.cs` - Account data operations

### 4. Services

Contains business logic and advanced LINQ operations:

- `IProductService.cs` / `ProductService.cs` - Product business logic with LINQ operations
- `ICategoryService.cs` / `CategoryService.cs` - Category business logic
- `IAccountService.cs` / `AccountService.cs` - Authentication business logic

### 5. WPFApp

The presentation layer with WPF user interface and code-behind files:

- `LoginWindow.xaml` - User authentication interface
- `MainWindow.xaml` - Main application interface with CRUD operations
- `App.xaml` - Application configuration

## Features

### Authentication

- Login with email and password using predefined credentials
- Default credentials: <<EMAIL>> / @1

### Product Management (CRUD)

- **Create**: Add new products with validation
- **Read**: View all products in a data grid
- **Update**: Modify existing product information
- **Delete**: Remove products with confirmation

### Advanced LINQ Operations

- **Search by Name**: Find products containing specific text
- **Filter by Category**: Show products from a selected category
- **Sort by Price**: Order products by price (descending)
- **Top Expensive**: Display top 5 most expensive products
- **Low Stock**: Show products with stock levels less than or equal to 15 units
- **Statistics**: Calculate average price, total inventory value, and category distribution

### LINQ Methods Demonstrated

- `Where()` - Filtering data
- `OrderBy()` / `OrderByDescending()` - Sorting data
- `Take()` - Limiting the number of results
- `GroupBy()` - Grouping data
- `Sum()` - Aggregating values
- `Average()` - Calculating averages
- `Count()` - Counting items
- `FirstOrDefault()` - Finding single items
- `Max()` - Finding maximum values
- `Any()` - Checking existence
- `Select()` - Projecting data
- `Join()` - Combining data from different collections

## Sample Data

The application comes with pre-loaded sample data:

### Categories

1. Beverages
2. Condiments
3. Confections
4. Dairy Products
5. Grains/Cereals
6. Meat/Poultry
7. Produce
8. Seafood

### Products

10 sample products across different categories with varying prices and stock levels.

## How to Run

1. Open the solution in Visual Studio 2019 or later
2. Ensure all projects are properly referenced
3. Build the solution (Build → Build Solution)
4. Set WPFApp as the startup project
5. Run the application (F5 or Debug → Start Debugging)

## Default Login Credentials

- Email: <<EMAIL>>
- Password: @1

## Technical Requirements

- .NET 8.0
- Windows OS (WPF requirement)
- Visual Studio 2019 or later

## Project Structure

ProductManagementDemo/
├── BusinessObjects/
│   ├── Product.cs
│   ├── Category.cs
│   └── AccountMember.cs
├── DataAccessLayer/
│   ├── ProductDAO.cs
│   ├── CategoryDAO.cs
│   └── AccountDAO.cs
├── Repositories/
│   ├── IProductRepository.cs
│   ├── ProductRepository.cs
│   ├── ICategoryRepository.cs
│   ├── CategoryRepository.cs
│   ├── IAccountRepository.cs
│   └── AccountRepository.cs
├── Services/
│   ├── IProductService.cs
│   ├── ProductService.cs
│   ├── ICategoryService.cs
│   ├── CategoryService.cs
│   ├── IAccountService.cs
│   └── AccountService.cs
└── WPFApp/
    ├── App.xaml
    ├── App.xaml.cs
    ├── LoginWindow.xaml
    ├── LoginWindow.xaml.cs
    ├── MainWindow.xaml
    └── MainWindow.xaml.cs

## Key Learning Objectives

- Layered architecture implementation
- Repository pattern usage
- LINQ to Objects operations
- WPF data binding
- Event handling in WPF
- Input validation
- Error handling
- In-memory data management
