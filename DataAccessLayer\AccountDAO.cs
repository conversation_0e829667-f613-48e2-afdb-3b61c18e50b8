using BusinessObjects;
using System.Linq;

namespace DataAccessLayer
{
    public class AccountDAO
    {
        private static AccountMember adminAccount = new AccountMember
        {
            MemberId = "PS0001",
            MemberPassword = "@1",
            FullName = "Administrator",
            EmailAddress = "<EMAIL>",
            MemberRole = 1
        };

        // Singleton pattern
        private static AccountDAO instance = null;
        private static readonly object instanceLock = new object();

        private AccountDAO() { }

        public static AccountDAO Instance
        {
            get
            {
                lock (instanceLock)
                {
                    if (instance == null)
                    {
                        instance = new AccountDAO();
                    }
                    return instance;
                }
            }
        }

        public AccountMember GetAccount(string email, string password)
        {
            if (adminAccount.EmailAddress == email && adminAccount.MemberPassword == password)
            {
                return adminAccount;
            }
            return null;
        }
    }
}


