using BusinessObjects;
using Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace WPFApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private IProductService productService;
        private ICategoryService categoryService;

        public MainWindow()
        {
            InitializeComponent();
            productService = new ProductService();
            categoryService = new CategoryService();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            LoadCategoryList();
            LoadProductList();
        }

        private void LoadCategoryList()
        {
            try
            {
                var categories = categoryService.GetCategories();
                cboCategory.ItemsSource = categories;
                lblStatus.Text = "Categories loaded successfully.";
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error loading categories: " + ex.Message, "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadProductList()
        {
            try
            {
                var products = productService.GetProducts();
                dgProducts.ItemsSource = products;
                lblStatus.Text = $"Loaded {products.Count} products.";
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error loading products: " + ex.Message, "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void dgProducts_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (dgProducts.SelectedItem is Product selectedProduct)
            {
                txtProductId.Text = selectedProduct.ProductId.ToString();
                txtProductName.Text = selectedProduct.ProductName;
                cboCategory.SelectedValue = selectedProduct.CategoryId;
                txtUnitsInStock.Text = selectedProduct.UnitsInStock?.ToString();
                txtUnitPrice.Text = selectedProduct.UnitPrice?.ToString();
            }
        }

        private void btnCreate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ValidateInput())
                {
                    Product newProduct = new Product
                    {
                        ProductName = txtProductName.Text.Trim(),
                        CategoryId = (int?)cboCategory.SelectedValue,
                        UnitsInStock = short.TryParse(txtUnitsInStock.Text, out short stock) ? stock : (short?)null,
                        UnitPrice = decimal.TryParse(txtUnitPrice.Text, out decimal price) ? price : (decimal?)null
                    };

                    productService.AddProduct(newProduct);
                    LoadProductList();
                    ClearForm();
                    lblStatus.Text = "Product created successfully.";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error creating product: " + ex.Message, "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnUpdate_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtProductId.Text))
                {
                    MessageBox.Show("Please select a product to update.", "Warning", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (ValidateInput())
                {
                    Product updatedProduct = new Product
                    {
                        ProductId = int.Parse(txtProductId.Text),
                        ProductName = txtProductName.Text.Trim(),
                        CategoryId = (int?)cboCategory.SelectedValue,
                        UnitsInStock = short.TryParse(txtUnitsInStock.Text, out short stock) ? stock : (short?)null,
                        UnitPrice = decimal.TryParse(txtUnitPrice.Text, out decimal price) ? price : (decimal?)null
                    };

                    productService.UpdateProduct(updatedProduct);
                    LoadProductList();
                    ClearForm();
                    lblStatus.Text = "Product updated successfully.";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error updating product: " + ex.Message, "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnDelete_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(txtProductId.Text))
                {
                    MessageBox.Show("Please select a product to delete.", "Warning", 
                                   MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                MessageBoxResult result = MessageBox.Show("Are you sure you want to delete this product?", 
                                                         "Confirm Delete", MessageBoxButton.YesNo, 
                                                         MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    int productId = int.Parse(txtProductId.Text);
                    productService.DeleteProduct(productId);
                    LoadProductList();
                    ClearForm();
                    lblStatus.Text = "Product deleted successfully.";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error deleting product: " + ex.Message, "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void btnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void btnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // LINQ Operation Button Handlers
        private void btnSearchByName_Click(object sender, RoutedEventArgs e)
        {
            string searchTerm = Microsoft.VisualBasic.Interaction.InputBox(
                "Enter product name to search:", "Search Products", "");
            
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var results = productService.SearchProductsByName(searchTerm);
                dgProducts.ItemsSource = results;
                lblStatus.Text = $"Found {results.Count} products matching '{searchTerm}'.";
            }
        }

        private void btnFilterByCategory_Click(object sender, RoutedEventArgs e)
        {
            if (cboCategory.SelectedValue != null)
            {
                int categoryId = (int)cboCategory.SelectedValue;
                var results = productService.GetProductsByCategory(categoryId);
                dgProducts.ItemsSource = results;
                lblStatus.Text = $"Showing {results.Count} products in selected category.";
            }
            else
            {
                MessageBox.Show("Please select a category first.", "Warning", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void btnSortByPrice_Click(object sender, RoutedEventArgs e)
        {
            var results = productService.GetProductsSortedByPrice(false); // Descending
            dgProducts.ItemsSource = results;
            lblStatus.Text = "Products sorted by price (highest to lowest).";
        }

        private void btnTopExpensive_Click(object sender, RoutedEventArgs e)
        {
            var results = productService.GetTopExpensiveProducts(5);
            dgProducts.ItemsSource = results;
            lblStatus.Text = "Showing top 5 most expensive products.";
        }

        private void btnLowStock_Click(object sender, RoutedEventArgs e)
        {
            var results = productService.GetLowStockProducts(15); // Threshold of 15
            dgProducts.ItemsSource = results;
            lblStatus.Text = $"Showing {results.Count} products with low stock (≤15 units).";
        }

        private void btnShowAll_Click(object sender, RoutedEventArgs e)
        {
            LoadProductList();
        }

        private void btnStatistics_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                decimal avgPrice = productService.GetAveragePrice();
                decimal totalValue = productService.GetTotalInventoryValue();
                var groupedProducts = productService.GetProductsGroupedByCategory();

                string stats = $"Product Statistics:\n\n";
                stats += $"Average Price: {avgPrice:C}\n";
                stats += $"Total Inventory Value: {totalValue:C}\n\n";
                stats += "Products by Category:\n";

                foreach (var group in groupedProducts)
                {
                    var category = categoryService.GetCategoryById(group.Key ?? 0);
                    string categoryName = category?.CategoryName ?? "Unknown";
                    stats += $"- {categoryName}: {group.Count()} products\n";
                }

                MessageBox.Show(stats, "Product Statistics", 
                               MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error calculating statistics: " + ex.Message, "Error", 
                               MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtProductName.Text))
            {
                MessageBox.Show("Product name is required.", "Validation Error", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                txtProductName.Focus();
                return false;
            }

            if (cboCategory.SelectedValue == null)
            {
                MessageBox.Show("Please select a category.", "Validation Error", 
                               MessageBoxButton.OK, MessageBoxImage.Warning);
                cboCategory.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            txtProductId.Text = "";
            txtProductName.Text = "";
            cboCategory.SelectedIndex = -1;
            txtUnitsInStock.Text = "";
            txtUnitPrice.Text = "";
            dgProducts.SelectedIndex = -1;
        }
    }
}

