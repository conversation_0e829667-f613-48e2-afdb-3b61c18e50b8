using BusinessObjects;
using Repositories;

namespace Services
{
    public class AccountService : IAccountService
    {
        private readonly IAccountRepository _accountRepository;

        public AccountService()
        {
            _accountRepository = new AccountRepository();
        }

        public AccountMember GetAccount(string email, string password)
        {
            return _accountRepository.GetAccount(email, password);
        }
    }
}


