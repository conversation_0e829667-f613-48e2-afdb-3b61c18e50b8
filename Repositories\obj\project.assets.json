{"version": 3, "targets": {"net8.0-windows7.0": {"BusinessObjects/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/BusinessObjects.dll": {}}, "runtime": {"bin/placeholder/BusinessObjects.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "DataAccessLayer/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"BusinessObjects": "1.0.0"}, "compile": {"bin/placeholder/DataAccessLayer.dll": {}}, "runtime": {"bin/placeholder/DataAccessLayer.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}}}, "libraries": {"BusinessObjects/1.0.0": {"type": "project", "path": "../BusinessObjects/BusinessObjects.csproj", "msbuildProject": "../BusinessObjects/BusinessObjects.csproj"}, "DataAccessLayer/1.0.0": {"type": "project", "path": "../DataAccessLayer/DataAccessLayer.csproj", "msbuildProject": "../DataAccessLayer/DataAccessLayer.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["BusinessObjects >= 1.0.0", "DataAccessLayer >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\Repositories\\Repositories.csproj", "projectName": "Repositories", "projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\Repositories\\Repositories.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\Repositories\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\BusinessObjects\\BusinessObjects.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\BusinessObjects\\BusinessObjects.csproj"}, "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\DataAccessLayer\\DataAccessLayer.csproj": {"projectPath": "D:\\FPTU\\StudyMaterials\\Summer 25\\PRN212\\Lab\\ProductManagementDemo\\DataAccessLayer\\DataAccessLayer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}