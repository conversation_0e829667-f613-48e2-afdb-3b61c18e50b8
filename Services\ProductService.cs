using BusinessObjects;
using Repositories;
using System.Collections.Generic;
using System.Linq;

namespace Services
{
    public class ProductService : IProductService
    {
        private readonly IProductRepository _productRepository;

        public ProductService()
        {
            _productRepository = new ProductRepository();
        }

        public void AddProduct(Product product)
        {
            _productRepository.AddProduct(product);
        }

        public void DeleteProduct(int productId)
        {
            _productRepository.DeleteProduct(productId);
        }

        public Product GetProductById(int productId)
        {
            return _productRepository.GetProductById(productId);
        }

        public List<Product> GetProducts()
        {
            return _productRepository.GetProducts();
        }

        public void UpdateProduct(Product product)
        {
            _productRepository.UpdateProduct(product);
        }

        // LINQ Operations Implementation
        public List<Product> GetProductsByCategory(int categoryId)
        {
            return _productRepository.GetProducts()
                .Where(p => p.CategoryId == categoryId)
                .ToList();
        }

        public List<Product> GetProductsByPriceRange(decimal minPrice, decimal maxPrice)
        {
            return _productRepository.GetProducts()
                .Where(p => p.UnitPrice.HasValue && 
                           p.UnitPrice.Value >= minPrice && 
                           p.UnitPrice.Value <= maxPrice)
                .ToList();
        }

        public List<Product> SearchProductsByName(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<Product>();

            return _productRepository.GetProducts()
                .Where(p => p.ProductName.ToLower().Contains(searchTerm.ToLower()))
                .ToList();
        }

        public List<Product> GetProductsSortedByPrice(bool ascending = true)
        {
            var products = _productRepository.GetProducts()
                .Where(p => p.UnitPrice.HasValue);

            return ascending 
                ? products.OrderBy(p => p.UnitPrice).ToList()
                : products.OrderByDescending(p => p.UnitPrice).ToList();
        }

        public List<Product> GetProductsSortedByStock(bool ascending = true)
        {
            var products = _productRepository.GetProducts()
                .Where(p => p.UnitsInStock.HasValue);

            return ascending 
                ? products.OrderBy(p => p.UnitsInStock).ToList()
                : products.OrderByDescending(p => p.UnitsInStock).ToList();
        }

        public List<Product> GetTopExpensiveProducts(int count)
        {
            return _productRepository.GetProducts()
                .Where(p => p.UnitPrice.HasValue)
                .OrderByDescending(p => p.UnitPrice)
                .Take(count)
                .ToList();
        }

        public List<Product> GetLowStockProducts(int threshold)
        {
            return _productRepository.GetProducts()
                .Where(p => p.UnitsInStock.HasValue && p.UnitsInStock.Value <= threshold)
                .OrderBy(p => p.UnitsInStock)
                .ToList();
        }

        public decimal GetAveragePrice()
        {
            var productsWithPrice = _productRepository.GetProducts()
                .Where(p => p.UnitPrice.HasValue)
                .Select(p => p.UnitPrice.Value);

            return productsWithPrice.Any() ? productsWithPrice.Average() : 0;
        }

        public decimal GetTotalInventoryValue()
        {
            return _productRepository.GetProducts()
                .Where(p => p.UnitPrice.HasValue && p.UnitsInStock.HasValue)
                .Sum(p => p.UnitPrice.Value * p.UnitsInStock.Value);
        }

        public int GetProductCountByCategory(int categoryId)
        {
            return _productRepository.GetProducts()
                .Count(p => p.CategoryId == categoryId);
        }

        public List<IGrouping<int?, Product>> GetProductsGroupedByCategory()
        {
            return _productRepository.GetProducts()
                .GroupBy(p => p.CategoryId)
                .ToList();
        }
    }
}

