using BusinessObjects;
using DataAccessLayer;
using System.Collections.Generic;

namespace Repositories
{
    public class CategoryRepository : ICategoryRepository
    {
        public List<Category> GetCategories()
        {
            return CategoryDAO.Instance.GetCategoryList();
        }

        public Category GetCategoryById(int categoryId)
        {
            return CategoryDAO.Instance.GetCategoryById(categoryId);
        }
    }
}


