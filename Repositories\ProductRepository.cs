using BusinessObjects;
using DataAccessLayer;
using System.Collections.Generic;

namespace Repositories
{
    public class ProductRepository : IProductRepository
    {
        public void AddProduct(Product product)
        {
            ProductDAO.Instance.AddNewProduct(product);
        }

        public void DeleteProduct(int productId)
        {
            ProductDAO.Instance.RemoveProduct(productId);
        }

        public Product GetProductById(int productId)
        {
            return ProductDAO.Instance.GetProductById(productId);
        }

        public List<Product> GetProducts()
        {
            return ProductDAO.Instance.GetProductList();
        }

        public void UpdateProduct(Product product)
        {
            ProductDAO.Instance.UpdateProduct(product);
        }
    }
}


