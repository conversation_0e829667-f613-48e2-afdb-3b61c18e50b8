<Window x:Class="WPFApp.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Login" Height="300" Width="400" 
        WindowStartupLocation="CenterScreen" ResizeMode="NoResize">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Text="Product Management System" 
                   FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="10,20,10,20"/>
        
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="20,10">
            <Label Content="Email:" Width="80" VerticalAlignment="Center"/>
            <TextBox x:Name="txtEmail" Width="250" Height="25" 
                     Text="<EMAIL>"/>
        </StackPanel>
        
        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="20,10">
            <Label Content="Password:" Width="80" VerticalAlignment="Center"/>
            <PasswordBox x:Name="txtPassword" Width="250" Height="25"/>
        </StackPanel>
        
        <StackPanel Grid.Row="3" Orientation="Horizontal" 
                    HorizontalAlignment="Center" Margin="20">
            <Button x:Name="btnLogin" Content="LOG IN" Width="80" Height="30" 
                    Margin="10,0" Click="btnLogin_Click"/>
            <Button x:Name="btnCancel" Content="CANCEL" Width="80" Height="30" 
                    Margin="10,0" Click="btnCancel_Click"/>
        </StackPanel>
        
        <TextBlock Grid.Row="4" x:Name="lblMessage" 
                   HorizontalAlignment="Center" Margin="10" 
                   Foreground="Red" FontWeight="Bold"/>
    </Grid>
</Window>

