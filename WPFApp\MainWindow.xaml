<Window x:Class="WPFApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Product Management" Height="700" Width="1000" 
        WindowStartupLocation="CenterScreen" Loaded="Window_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Title -->
        <TextBlock Grid.Row="0" Text="Product Management System" 
                   FontSize="20" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="10"/>
        
        <!-- Input Form -->
        <GroupBox Grid.Row="1" Header="Product Information" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Label Grid.Row="0" Grid.Column="0" Content="Product ID:" 
                       VerticalAlignment="Center" Margin="5"/>
                <TextBox Grid.Row="0" Grid.Column="1" x:Name="txtProductId" 
                         Height="25" Margin="5" IsReadOnly="True"/>
                
                <Label Grid.Row="0" Grid.Column="2" Content="Product Name:" 
                       VerticalAlignment="Center" Margin="5"/>
                <TextBox Grid.Row="0" Grid.Column="3" x:Name="txtProductName" 
                         Height="25" Margin="5"/>
                
                <Label Grid.Row="1" Grid.Column="0" Content="Category:" 
                       VerticalAlignment="Center" Margin="5"/>
                <ComboBox Grid.Row="1" Grid.Column="1" x:Name="cboCategory" 
                          Height="25" Margin="5" DisplayMemberPath="CategoryName" 
                          SelectedValuePath="CategoryId"/>
                
                <Label Grid.Row="1" Grid.Column="2" Content="Units In Stock:" 
                       VerticalAlignment="Center" Margin="5"/>
                <TextBox Grid.Row="1" Grid.Column="3" x:Name="txtUnitsInStock" 
                         Height="25" Margin="5"/>
                
                <Label Grid.Row="2" Grid.Column="0" Content="Unit Price:" 
                       VerticalAlignment="Center" Margin="5"/>
                <TextBox Grid.Row="2" Grid.Column="1" x:Name="txtUnitPrice" 
                         Height="25" Margin="5"/>
                
                <!-- Buttons -->
                <StackPanel Grid.Row="3" Grid.ColumnSpan="4" Orientation="Horizontal" 
                            HorizontalAlignment="Center" Margin="10">
                    <Button x:Name="btnCreate" Content="Create" Width="80" Height="30" 
                            Margin="5" Click="btnCreate_Click"/>
                    <Button x:Name="btnUpdate" Content="Update" Width="80" Height="30" 
                            Margin="5" Click="btnUpdate_Click"/>
                    <Button x:Name="btnDelete" Content="Delete" Width="80" Height="30" 
                            Margin="5" Click="btnDelete_Click"/>
                    <Button x:Name="btnClear" Content="Clear" Width="80" Height="30" 
                            Margin="5" Click="btnClear_Click"/>
                </StackPanel>
            </Grid>
        </GroupBox>
        
        <!-- LINQ Operations -->
        <GroupBox Grid.Row="2" Header="LINQ Operations" Margin="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="btnSearchByName" Content="Search by Name" Width="120" Height="30" 
                        Margin="5" Click="btnSearchByName_Click"/>
                <Button x:Name="btnFilterByCategory" Content="Filter by Category" Width="120" Height="30" 
                        Margin="5" Click="btnFilterByCategory_Click"/>
                <Button x:Name="btnSortByPrice" Content="Sort by Price" Width="100" Height="30" 
                        Margin="5" Click="btnSortByPrice_Click"/>
                <Button x:Name="btnTopExpensive" Content="Top 5 Expensive" Width="120" Height="30" 
                        Margin="5" Click="btnTopExpensive_Click"/>
                <Button x:Name="btnLowStock" Content="Low Stock" Width="80" Height="30" 
                        Margin="5" Click="btnLowStock_Click"/>
                <Button x:Name="btnShowAll" Content="Show All" Width="80" Height="30" 
                        Margin="5" Click="btnShowAll_Click"/>
                <Button x:Name="btnStatistics" Content="Statistics" Width="80" Height="30" 
                        Margin="5" Click="btnStatistics_Click"/>
            </StackPanel>
        </GroupBox>
        
        <!-- Product List -->
        <GroupBox Grid.Row="3" Header="Product List" Margin="10">
            <DataGrid x:Name="dgProducts" AutoGenerateColumns="False" 
                      SelectionMode="Single" CanUserAddRows="False" 
                      SelectionChanged="dgProducts_SelectionChanged">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="Product ID" Binding="{Binding ProductId}" 
                                        Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Product Name" Binding="{Binding ProductName}" 
                                        Width="200" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Category ID" Binding="{Binding CategoryId}" 
                                        Width="80" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Units In Stock" Binding="{Binding UnitsInStock}" 
                                        Width="100" IsReadOnly="True"/>
                    <DataGridTextColumn Header="Unit Price" Binding="{Binding UnitPrice, StringFormat=C}" 
                                        Width="100" IsReadOnly="True"/>
                </DataGrid.Columns>
            </DataGrid>
        </GroupBox>
        
        <!-- Status and Close -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="10">
            <TextBlock x:Name="lblStatus" VerticalAlignment="Center" 
                       Margin="10,0" FontWeight="Bold"/>
            <Button x:Name="btnClose" Content="Close" Width="80" Height="30" 
                    Margin="5" Click="btnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>

