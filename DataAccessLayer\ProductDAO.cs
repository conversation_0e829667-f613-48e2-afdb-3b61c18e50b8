using BusinessObjects;
using System.Collections.Generic;
using System.Linq;

namespace DataAccessLayer
{
    public class ProductDAO
    {
        private static List<Product> ProductList = new List<Product>()
        {
            new Product { ProductId = 1, ProductName = "Chai", CategoryId = 3, UnitsInStock = 12, UnitPrice = 18.00m },
            new Product { ProductId = 2, ProductName = "Chang", CategoryId = 1, UnitsInStock = 23, UnitPrice = 19.00m },
            new Product { ProductId = 3, ProductName = "Aniseed Syrup", CategoryId = 2, UnitsInStock = 23, UnitPrice = 10.00m },
            new Product { ProductId = 4, ProductName = "Chef Anton's Cajun Seasoning", CategoryId = 2, UnitsInStock = 34, UnitPrice = 22.00m },
            new Product { ProductId = 5, ProductName = "Chef Anton's Gumbo Mix", CategoryId = 2, UnitsInStock = 45, UnitPrice = 21.35m },
            new Product { ProductId = 6, ProductName = "Grandma's Boysenberry Spread", CategoryId = 2, UnitsInStock = 21, UnitPrice = 25.00m },
            new Product { ProductId = 7, ProductName = "Uncle Bob's Organic Dried Pears", CategoryId = 7, UnitsInStock = 22, UnitPrice = 30.00m },
            new Product { ProductId = 8, ProductName = "Northwoods Cranberry Sauce", CategoryId = 2, UnitsInStock = 10, UnitPrice = 40.00m },
            new Product { ProductId = 9, ProductName = "Mishi Kobe Niku", CategoryId = 6, UnitsInStock = 12, UnitPrice = 97.00m },
            new Product { ProductId = 10, ProductName = "Ikura", CategoryId = 8, UnitsInStock = 13, UnitPrice = 31.00m }
        };

        // Singleton pattern
        private static ProductDAO instance = null;
        private static readonly object instanceLock = new object();

        private ProductDAO() { }

        public static ProductDAO Instance
        {
            get
            {
                lock (instanceLock)
                {
                    if (instance == null)
                    {
                        instance = new ProductDAO();
                    }
                    return instance;
                }
            }
        }

        public List<Product> GetProductList()
        {
            return ProductList;
        }

        public Product GetProductById(int productId)
        {
            return ProductList.FirstOrDefault(p => p.ProductId == productId);
        }

        public void AddNewProduct(Product product)
        {
            product.ProductId = ProductList.Max(p => p.ProductId) + 1;
            ProductList.Add(product);
        }

        public void UpdateProduct(Product product)
        {
            Product existingProduct = GetProductById(product.ProductId);
            if (existingProduct != null)
            {
                existingProduct.ProductName = product.ProductName;
                existingProduct.CategoryId = product.CategoryId;
                existingProduct.UnitsInStock = product.UnitsInStock;
                existingProduct.UnitPrice = product.UnitPrice;
            }
        }

        public void RemoveProduct(int productId)
        {
            Product productToRemove = GetProductById(productId);
            if (productToRemove != null)
            {
                ProductList.Remove(productToRemove);
            }
        }
    }
}


